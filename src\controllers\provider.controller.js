const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const GlobalService = require('../services/GlobalService');
const pick = require('../utils/pick');
const { userService } = require('../services');
const backgroundEmailService = require('../services/backgroundEmail.service');
const { convertCoordinatesToLocation } = require('../utils/coordinateHelper');

const modelName = 'Provider';
const resourceName = 'Provider';
const resourcesName = 'Providers';
const aggregation = [
  {
    $lookup: {
      from: 'users',
      localField: 'user',
      foreignField: '_id',
      as: 'userDetails',
    },
  },
  {
    $addFields: {
      email: { $arrayElemAt: ['$userDetails.email', 0] },
    },
  },
  {
    $project: {
      userDetails: 0,
    },
  },
];

const create = catchAsync(async (req, res) => {
  let userData = pick(req.body, ['email', 'password']);
  userData['role'] = 'provider';
  const user = await userService.createUser(userData);
  req.body['user'] = user._id;

  // Convert coordinates to location using helper function
  convertCoordinatesToLocation(req.body);

  const provider = await GlobalService.create(modelName, req.body);
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, provider);
});

const index = catchAsync(async (req, res) => {
  const options = req.queryOptions || { ...req.query };
  const searchFilter = req.searchFilter;
  let pipeline = [...aggregation];

  if (req.customer && req.customer._id) {
    pipeline.push(
      { $match: { isApproved: "approved" } },
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'provider',
          as: 'products',
        },
      },
      // Unwind the products array to perform lookups for categories
      { $unwind: { path: '$products', preserveNullAndEmptyArrays: true } },
      // Lookup to fetch category details for each product
      {
        $lookup: {
          from: 'categories',
          localField: 'products.category',
          foreignField: '_id',
          as: 'products.categoryDetails',
        },
      },
      // Add the category name to each product
      {
        $addFields: {
          'products.categoryName': { $arrayElemAt: ['$products.categoryDetails.name', 0] },
        },
      },
      // Group products back into an array, grouped by provider
      {
        $group: {
          _id: '$_id',
          root: { $first: '$$ROOT' },
          products: { $push: '$products' },
        },
      },
      // Replace the root with the original document and add the grouped products
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: ['$root', { products: '$products' }],
          },
        },
      },
      // Group products by category and include the category name
      {
        $addFields: {
          productsByCategory: {
            $reduce: {
              input: '$products',
              initialValue: [],
              in: {
                $cond: [
                  { $in: ['$$this.category', '$$value.category'] },
                  {
                    $map: {
                      input: '$$value',
                      as: 'cat',
                      in: {
                        $cond: [
                          { $eq: ['$$cat.category', '$$this.category'] },
                          {
                            category: '$$cat.category',
                            categoryName: '$$cat.categoryName',
                            products: { $concatArrays: ['$$cat.products', ['$$this']] },
                          },
                          '$$cat',
                        ],
                      },
                    },
                  },
                  {
                    $concatArrays: [
                      '$$value',
                      [
                        {
                          category: '$$this.category',
                          categoryName: '$$this.categoryName',
                          products: ['$$this'],
                        },
                      ],
                    ],
                  },
                ],
              },
            },
          },
        },
      },
      // Remove the individual products array if you only want the grouped data
      {
        $project: {
          products: 0,
        },
      }
    );
  }

  const providers = await GlobalService.getAll(modelName, options, pipeline, searchFilter);
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, providers);
});

const view = catchAsync(async (req, res) => {
  // Handle different parameter names from different routes
  const providerId = req.params.provider || req.params.providerId || req.params.id;

  if (!providerId) {
    return sendError(res, 'Provider ID is required', httpStatus.BAD_REQUEST);
  }

  console.log('Looking for provider with ID:', providerId); // Debug log

  const provider = await GlobalService.getById(modelName, providerId, aggregation);
  if (!provider) {
    return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  }
  sendSuccess(res, `${resourceName} fetched successfully!`, httpStatus.OK, provider);
});

const update = catchAsync(async (req, res) => {
  // 1. Handle coordinates conversion using helper function
  convertCoordinatesToLocation(req.body, true); // strict mode for update

  try {
    // 2. Get provider ID from either URL params or request body
    const providerId = req.params.id || req.body.provider;
    if (!providerId) {
      return sendError(res, 'Provider ID is required (either in URL or request body)', httpStatus.BAD_REQUEST);
    }

    // Remove provider from body to avoid conflicts
    const updateData = { ...req.body };
    delete updateData.provider;

    // 3. Update provider with validation
    const provider = await GlobalService.updateById(
      modelName,
      providerId,
      updateData,
      aggregation,
      { runValidators: true } // ENABLE VALIDATORS
    );

    // 3. Update associated user email if needed
    if (req.body.email) {
      await GlobalService.updateById(
        "User",
        provider.user,
        { email: req.body.email }
      );
    }

    sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, provider);
  } catch (error) {
    // Handle validation errors specifically
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(el => el.message);
      throw new ApiError(httpStatus.BAD_REQUEST, `Validation failed: ${errors.join(', ')}`);
    }
    throw error;
  }
});

const softDelete = catchAsync(async (req, res) => {
  const provider = await GlobalService.getById(modelName, req.params.provider, aggregation);
  if (!provider) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  await GlobalService.softDeleteById('User', ['email'], provider.user);
  await GlobalService.softDeleteById(modelName, [], req.params.provider);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

// const status = catchAsync(async (req, res) => {
//   const provider = await GlobalService.updateById(modelName, req.body.provider, pick(req.body, ['isApproved']));
//   sendSuccess(res, `${resourceName} status changed successfully!`, httpStatus.OK, provider);
// });

const status = catchAsync(async (req, res) => {
  // Get the current provider data before updating to compare status change
  const currentProvider = await GlobalService.getById(modelName, req.body.provider, aggregation);
  if (!currentProvider) {
    return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  }

  // Update the provider status
  const provider = await GlobalService.updateById(modelName, req.body.provider, pick(req.body, ['isApproved']));

  // Check if status actually changed and queue email notification
  const newStatus = req.body.isApproved;
  const oldStatus = currentProvider.isApproved;

  if (newStatus && newStatus !== oldStatus) {
    // Queue email for background processing
    const jobId = backgroundEmailService.queueProviderStatusChangeEmail(
      currentProvider.email,
      currentProvider.name,
      newStatus
    );
    console.log(`Email queued (Job ID: ${jobId}) for ${currentProvider.email} - status: ${newStatus}`);
  }

  sendSuccess(res, `${resourceName} status changed successfully!`, httpStatus.OK, provider);
});

const updateProvider = catchAsync(async (req, res) => {
  req.body['user'] = req.user._id;

  // Convert coordinates to location using global helper function
  convertCoordinatesToLocation(req.body);

  let provider;
  if (req.provider._id) provider = await GlobalService.updateById(modelName, req.provider._id, req.body, aggregation);
  else provider = await GlobalService.create(modelName, req.body);
  if (req.body.email) await GlobalService.updateById('User', req.user._id, pick(req.body, ['email']));
  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, provider);
});

// Complete profile route - returns all provider data with related information
const getProfile = catchAsync(async (req, res) => {
  const provider = req.provider;

  if (!provider || !provider._id) {
    return sendError(res, 'Provider not found', httpStatus.NOT_FOUND);
  }

  const mongoose = require('mongoose');
  const providerId = mongoose.Types.ObjectId(provider._id);

  try {
    // Get complete provider data using aggregation pipeline
    const completeProvider = await GlobalService.getById(modelName, providerId, [
      // Lookup user details for email
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userDetails',
          pipeline: [{ $project: { email: 1 } }]
        }
      },
      {
        $addFields: {
          email: { $arrayElemAt: ['$userDetails.email', 0] }
        }
      },
      // Lookup products count
      {
        $lookup: {
          from: 'products',
          localField: '_id',
          foreignField: 'provider',
          as: 'products'
        }
      },
      {
        $addFields: {
          totalProducts: { $size: '$products' }
        }
      },
      // Lookup orders count
      {
        $lookup: {
          from: 'orders',
          localField: '_id',
          foreignField: 'provider_id',
          as: 'orders'
        }
      },
      {
        $addFields: {
          totalOrders: { $size: '$orders' }
        }
      },
      // Lookup reviews and calculate average rating
      {
        $lookup: {
          from: 'reviews',
          let: { providerId: '$_id' },
          pipeline: [
            {
              $lookup: {
                from: 'orders',
                localField: 'order_id',
                foreignField: '_id',
                as: 'order'
              }
            },
            {
              $unwind: '$order'
            },
            {
              $match: {
                $expr: { $eq: ['$order.provider_id', '$$providerId'] }
              }
            }
          ],
          as: 'reviews'
        }
      },
      {
        $addFields: {
          totalReviews: { $size: '$reviews' },
          averageRating: {
            $cond: {
              if: { $gt: [{ $size: '$reviews' }, 0] },
              then: { $avg: '$reviews.rating' },
              else: 0
            }
          }
        }
      },
      // Clean up temporary fields
      {
        $project: {
          userDetails: 0,
          products: 0,
          orders: 0,
          reviews: 0
        }
      }
    ]);

    if (!completeProvider) {
      return sendError(res, 'Provider not found', httpStatus.NOT_FOUND);
    }

    sendSuccess(res, 'Provider profile fetched successfully!', httpStatus.OK, completeProvider);
  } catch (error) {
    console.error('Error fetching complete provider profile:', error);
    return sendError(res, 'Error fetching provider profile', httpStatus.INTERNAL_SERVER_ERROR);
  }
});

// Provider Dashboard - Get counts for reviews, orders, and products
const getDashboard = catchAsync(async (req, res) => {
  const provider = req.provider;

  if (!provider || !provider._id) {
    return sendError(res, 'Provider not found', httpStatus.NOT_FOUND);
  }

  const { Product, Order, Review } = require('../models');
  const mongoose = require('mongoose');

  try {
    const providerId = mongoose.Types.ObjectId(provider._id);

    // Get total products count for this provider
    const totalProducts = await Product.countDocuments({ provider: providerId });

    // Get total orders count for this provider
    const totalOrders = await Order.countDocuments({ provider_id: providerId });

    // Get total reviews count for this provider's orders
    // First get all order IDs for this provider, then count reviews for those orders
    const providerOrderIds = await Order.find({ provider_id: providerId }).select('_id').lean();
    const orderIds = providerOrderIds.map(order => order._id);
    const totalReviews = await Review.countDocuments({ order_id: { $in: orderIds } });

    const dashboardData = {
      totalProducts,
      totalOrders,
      totalReviews,
    };

    sendSuccess(res, 'Dashboard data fetched successfully!', httpStatus.OK, dashboardData);
  } catch (error) {
    console.error('Dashboard error:', error);
    return sendError(res, 'Failed to fetch dashboard data', httpStatus.INTERNAL_SERVER_ERROR);
  }
});

module.exports = { create, index, view, update, softDelete, status, updateProvider, getProfile, getDashboard };
