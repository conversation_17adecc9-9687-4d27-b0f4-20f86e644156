const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const GlobalService = require('../services/GlobalService');
const pick = require('../utils/pick');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const mongoose = require('mongoose');

const modelName = 'Review';
const resourceName = 'Review';
const resourcesName = 'Reviews';
const uniqueAttribs = [];

// Full aggregation for detailed review view
const fullAggregation = [
  {
    $lookup: {
      from: 'orders',
      localField: 'order_id',
      foreignField: '_id',
      as: 'order',
      pipeline: [{ $project: { __v: 0 } }],
    },
  },
  {
    $unwind: {
      path: '$order',
      preserveNullAndEmptyArrays: true,
    },
  },
];

// Optimized aggregation for review list with only required fields
const listAggregation = [
  {
    $lookup: {
      from: 'orders',
      localField: 'order_id',
      foreignField: '_id',
      as: 'order',
      pipeline: [{ $project: { user_id: 1, product_id: 1, provider_id: 1 } }],
    },
  },
  {
    $unwind: {
      path: '$order',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $lookup: {
      from: 'products',
      localField: 'order.product_id',
      foreignField: '_id',
      as: 'product',
      pipeline: [{ $project: { name: 1 } }],
    },
  },
  {
    $unwind: {
      path: '$product',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $lookup: {
      from: 'users',
      localField: 'order.user_id',
      foreignField: '_id',
      as: 'customer',
      pipeline: [{ $project: { email: 1 } }],
    },
  },
  {
    $unwind: {
      path: '$customer',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $lookup: {
      from: 'customers',
      localField: 'order.user_id',
      foreignField: 'user',
      as: 'customerProfile',
      pipeline: [{ $project: { username: 1 } }],
    },
  },
  {
    $unwind: {
      path: '$customerProfile',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $lookup: {
      from: 'providers',
      localField: 'order.provider_id',
      foreignField: '_id',
      as: 'provider',
      pipeline: [{ $project: { name: 1 } }],
    },
  },
  {
    $unwind: {
      path: '$provider',
      preserveNullAndEmptyArrays: true,
    },
  },
  {
    $project: {
      id: '$_id',
      product_id: '$order.product_id',
      product_name: '$product.name',
      rating: 1,
      title: 1,
      experience: 1,
      reviewed_on: '$createdAt',
      customer_name: { $ifNull: ['$customerProfile.username', '$customer.email'] },
      customer_id: '$order.user_id',
      order_id: '$order_id',
      provider_id: '$order.provider_id',
      provider_name: '$provider.name'
    }
  }
];

//  Create Review
const create = catchAsync(async (req, res) => {
  const reviewData = pick(req.body, ['order_id', 'rating', 'title', 'experience' , 'image']);

  const review = await GlobalService.create(modelName, reviewData);
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, review);
});

//  Get Reviews with flexible filtering (optimized with only required fields)
const getReviews = catchAsync(async (req, res) => {
    const options = req.queryOptions || pick(req.query, ['sortBy', 'limit', 'page']);
    let searchFilter = req.searchFilter;

    // Build filter object based on available parameters
    const filter = {};

    // Check for userId in both params and query
    const userId = req.params.userId || req.query.userId;
    if (userId) {
        if (!mongoose.Types.ObjectId.isValid(userId)) {
            return sendError(res, 'Invalid user ID', httpStatus.BAD_REQUEST);
        }
        filter['order.user_id'] = mongoose.Types.ObjectId(userId);
    }

    // Check for productId in both params and query
    const productId = req.params.productId || req.query.productId;
    if (productId) {
        if (!mongoose.Types.ObjectId.isValid(productId)) {
            return sendError(res, 'Invalid product ID', httpStatus.BAD_REQUEST);
        }
        filter['order.product_id'] = mongoose.Types.ObjectId(productId);
    }

    // Check for providerId in both params and query
    const providerId = req.params.providerId || req.query.providerId;
    if (providerId) {
        if (!mongoose.Types.ObjectId.isValid(providerId)) {
            return sendError(res, 'Invalid provider ID', httpStatus.BAD_REQUEST);
        }
        filter['order.provider_id'] = mongoose.Types.ObjectId(providerId);
    }

    // Auto-filter by logged-in provider's ID (similar to product controller)
    if (req.provider && req.provider._id && !providerId) {
        filter['order.provider_id'] = mongoose.Types.ObjectId(req.provider._id);
    }

    // Create optimized aggregation pipeline
    let optimizedAggregation = [...listAggregation];

    // Apply provider/user/product filters at the right stage (after order lookup)
    if (Object.keys(filter).length > 0) {
        optimizedAggregation.splice(2, 0, { $match: filter });
    }

    const reviews = await GlobalService.getAll(modelName, options, optimizedAggregation, searchFilter);

    sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, reviews);
});
// Update Review
const update = catchAsync(async (req, res) => {
  const reviewId = req.body.review_id;
  const existingReview = await GlobalService.getById(modelName, reviewId, fullAggregation);
  if (!existingReview) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);

  const updatedData = pick(req.body, ['rating', 'title', 'experience' , 'image']);

  const updatedReview = await GlobalService.updateById(modelName, reviewId, updatedData, fullAggregation);
  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, updatedReview);
});

// Soft Delete Review
const softDelete = catchAsync(async (req, res) => {
  const reviewId = req.body.review_id;
  const review = await GlobalService.getById(modelName, reviewId);

  if (!review) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);

  await GlobalService.softDeleteById(modelName, uniqueAttribs, reviewId);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

// Admin Delete Review (for admin routes)
const adminDelete = catchAsync(async (req, res) => {
  const reviewId = req.body.reviewId;
  const review = await GlobalService.getById(modelName, reviewId);

  if (!review) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);

  await GlobalService.softDeleteById(modelName, uniqueAttribs, reviewId);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

module.exports = {
  create,
  getReviews,
  update,
  softDelete,
  adminDelete,
};
