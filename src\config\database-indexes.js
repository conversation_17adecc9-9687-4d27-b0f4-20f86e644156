const mongoose = require('mongoose');

/**
 * Database indexes for optimal recommendation performance
 * Run this script to create indexes for the recommendation system
 */

const createRecommendationIndexes = async () => {
  try {
    const db = mongoose.connection.db;

    console.log('Creating indexes for recommendation system...');

    // UserPreference indexes
    await db.collection('userpreferences').createIndex(
      { user: 1, question: 1 },
      { unique: true, name: 'user_question_unique' }
    );

    await db.collection('userpreferences').createIndex(
      { user: 1 },
      { name: 'user_preferences_lookup' }
    );

    await db.collection('userpreferences').createIndex(
      { question: 1 },
      { name: 'question_preferences_lookup' }
    );

    await db.collection('userpreferences').createIndex(
      { answer: 1 },
      { name: 'answer_preferences_lookup' }
    );

    // ProductUsages indexes
    await db.collection('productusages').createIndex(
      { product: 1, question: 1 },
      { name: 'product_question_lookup' }
    );

    await db.collection('productusages').createIndex(
      { product: 1 },
      { name: 'product_usages_lookup' }
    );

    await db.collection('productusages').createIndex(
      { question: 1, option: 1 },
      { name: 'question_option_lookup' }
    );

    await db.collection('productusages').createIndex(
      { option: 1 },
      { name: 'option_usages_lookup' }
    );

    await db.collection('productusages').createIndex(
      { deleted: 1, product: 1 },
      { name: 'active_product_usages' }
    );

    // Product indexes
    await db.collection('products').createIndex(
      { provider: 1 },
      { name: 'provider_products_lookup' }
    );

    await db.collection('products').createIndex(
      { category: 1 },
      { name: 'category_products_lookup' }
    );

    await db.collection('products').createIndex(
      { category: 1, subCategory: 1 },
      { name: 'category_subcategory_lookup' }
    );

    await db.collection('products').createIndex(
      { createdAt: -1 },
      { name: 'products_by_date' }
    );

    // Question indexes
    await db.collection('questions').createIndex(
      { sequence: 1 },
      { name: 'questions_by_sequence' }
    );

    await db.collection('questions').createIndex(
      { deleted: 1, sequence: 1 },
      { name: 'active_questions_by_sequence' }
    );

    // Option indexes
    await db.collection('options').createIndex(
      { question: 1 },
      { name: 'question_options_lookup' }
    );

    await db.collection('options').createIndex(
      { deleted: 1, question: 1 },
      { name: 'active_question_options' }
    );

    // Variant indexes
    await db.collection('variants').createIndex(
      { product: 1 },
      { name: 'product_variants_lookup' }
    );

    await db.collection('variants').createIndex(
      { deleted: 1, product: 1 },
      { name: 'active_product_variants' }
    );

    // Provider indexes
    await db.collection('providers').createIndex(
      { isApproved: 1 },
      { name: 'approved_providers' }
    );

    await db.collection('providers').createIndex(
      { deleted: 1, isApproved: 1 },
      { name: 'active_approved_providers' }
    );

    // Category and SubCategory indexes
    await db.collection('categories').createIndex(
      { status: 1 },
      { name: 'active_categories' }
    );

    await db.collection('categories').createIndex(
      { deleted: 1, status: 1 },
      { name: 'active_undeleted_categories' }
    );

    await db.collection('subcategories').createIndex(
      { category: 1 },
      { name: 'category_subcategories' }
    );

    await db.collection('subcategories').createIndex(
      { status: 1 },
      { name: 'active_subcategories' }
    );

    await db.collection('subcategories').createIndex(
      { deleted: 1, status: 1, category: 1 },
      { name: 'active_category_subcategories' }
    );

    // Compound indexes for complex queries
    await db.collection('productusages').createIndex(
      { deleted: 1, question: 1, option: 1 },
      { name: 'active_question_option_usages' }
    );

    await db.collection('userpreferences').createIndex(
      { user: 1, createdAt: -1 },
      { name: 'user_preferences_by_date' }
    );

    await db.collection('products').createIndex(
      { category: 1, createdAt: -1 },
      { name: 'category_products_by_date' }
    );

    console.log('✅ All recommendation indexes created successfully!');

    // Display index information
    const collections = [
      'userpreferences',
      'productusages', 
      'products',
      'questions',
      'options',
      'variants',
      'providers',
      'categories',
      'subcategories'
    ];

    for (const collectionName of collections) {
      const indexes = await db.collection(collectionName).indexes();
      console.log(`\n📊 ${collectionName} indexes:`, indexes.length);
      indexes.forEach(index => {
        console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
      });
    }

  } catch (error) {
    console.error('❌ Error creating indexes:', error);
    throw error;
  }
};

/**
 * Drop all recommendation-related indexes
 * Use with caution - only for development/testing
 */
const dropRecommendationIndexes = async () => {
  try {
    const db = mongoose.connection.db;
    
    console.log('Dropping recommendation indexes...');

    const indexesToDrop = [
      'user_question_unique',
      'user_preferences_lookup',
      'question_preferences_lookup',
      'answer_preferences_lookup',
      'product_question_lookup',
      'product_usages_lookup',
      'question_option_lookup',
      'option_usages_lookup',
      'active_product_usages',
      'provider_products_lookup',
      'category_products_lookup',
      'category_subcategory_lookup',
      'products_by_date',
      'questions_by_sequence',
      'active_questions_by_sequence',
      'question_options_lookup',
      'active_question_options',
      'product_variants_lookup',
      'active_product_variants',
      'approved_providers',
      'active_approved_providers',
      'active_categories',
      'active_undeleted_categories',
      'category_subcategories',
      'active_subcategories',
      'active_category_subcategories',
      'active_question_option_usages',
      'user_preferences_by_date',
      'category_products_by_date'
    ];

    const collections = [
      'userpreferences',
      'productusages',
      'products',
      'questions',
      'options',
      'variants',
      'providers',
      'categories',
      'subcategories'
    ];

    for (const collectionName of collections) {
      for (const indexName of indexesToDrop) {
        try {
          await db.collection(collectionName).dropIndex(indexName);
          console.log(`✅ Dropped index ${indexName} from ${collectionName}`);
        } catch (error) {
          // Index might not exist, continue
          if (!error.message.includes('index not found')) {
            console.log(`⚠️  Could not drop ${indexName} from ${collectionName}: ${error.message}`);
          }
        }
      }
    }

    console.log('✅ Recommendation indexes dropped successfully!');

  } catch (error) {
    console.error('❌ Error dropping indexes:', error);
    throw error;
  }
};

module.exports = {
  createRecommendationIndexes,
  dropRecommendationIndexes
};
